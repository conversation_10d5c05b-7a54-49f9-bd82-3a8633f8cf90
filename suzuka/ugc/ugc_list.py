import hashlib
import json

from django.contrib.sites.models import Site
from slumber_party.utils import SlumberResourceIterator, SlumberResourceList

from suzuka.classifieds.utils import get_site_id
from suzuka.conf.sites import (
    current_site,
)

from .settings import UGC_LIST_CACHE_TIMEOUT, UGC_LIST_MAX_ITEMS
from .utils import (
    cache_get,
    cache_set,
    process_ugc,
    ugc_api,
)


class BaseUGCList:
    """
    Base class for a ugc list that implements querying the ugc API.
    Used for the ``UGCList`` model.
    """

    MAX_PINNED = 30

    @property
    def ids_only(self) -> bool:
        return False

    def ugc(
        self,
        limit=None,
        use_cache=True,
        set_cache=True,
        ids_only=None,
        pinning=True,
        filters=None,
        pinned_filters=None,
        site_id=None,
        show_all_sites=False,
    ):
        """
        Retrieves ugc from the API for the selected criteria
        specified.
        """
        if ids_only is None:
            ids_only = self.ids_only
        else:
            # Don't allow `ids_only` argument to override and disable
            # the `ids_only` attribute on the object. (For a `UGCList`
            # object, this comes from the `pinned_ugc_only` field.)
            ids_only = ids_only or self.ids_only
        pinned_ugc_ids = getattr(self, "pinned_ugc_ids_array", None)
        use_pinning = bool(pinning and pinned_ugc_ids and any(pinned_ugc_ids))

        site = (
            Site.objects.select_related("settings").get(id=site_id)
            if site_id
            else current_site()
        )
        site_id = get_site_id(site)

        if limit is None:
            limit = getattr(self, "limit", None)
        if limit is None:
            limit = (
                BaseUGCList.MAX_PINNED if use_pinning else UGC_LIST_MAX_ITEMS
            )

        if filters is None:
            filters = {}
        if pinned_filters is None:
            pinned_filters = {"publishable": True}

        filters["publishable"] = True
        filters["content_types"] = ",".join(getattr(self, "content_types", []))
        filters["limit"] = limit

        api = ugc_api()
        ugc_list_id = getattr(self, "id", None)
        if use_pinning:
            pins = [str(x) for x in pinned_ugc_ids if x]
            pins_str = ",".join(pins)

            filters["id_not_in"] = pins_str

            def sort_key(s):
                return pins.index(str(s["id"]))

            pinned = PinnedUGCResourceList(
                api.ugc,
                sort_key=sort_key,
                resource_class=UGCResourceList,
                id__in=pins_str,
                ugc_list_id=ugc_list_id,
                use_cache=use_cache,
                set_cache=set_cache,
                pins=pinned_ugc_ids,
                site_id=site_id,
                **pinned_filters,
            )
            if not ids_only:
                unpinned = PinnedUGCResourceList(
                    api.ugc,
                    resource_class=UGCResourceList,
                    ugc_list_id=ugc_list_id,
                    use_cache=use_cache,
                    set_cache=set_cache,
                    pins=pinned_ugc_ids,
                    site_id=site_id,
                    **filters,
                )
                ugc_list = pinned + unpinned

            else:
                ugc_list = pinned

        elif not ids_only:
            ugc_list = SlumberResourceList(
                api.ugc,
                resource_class=UGCResourceList,
                ugc_list_id=ugc_list_id,
                use_cache=use_cache,
                site_id=site_id,
                set_cache=set_cache,
                **filters,
            )

        else:
            ugc_list = []

        return ugc_list

    def uncached_ugc(self, limit=None, **kwargs):
        kwargs.setdefault("set_cache", False)
        return self.ugc(limit=limit, use_cache=False, **kwargs)


class PinnedUGCResourceList(SlumberResourceList):
    def __init__(self, *args, **kwargs):
        self._pins = list(kwargs.pop("pins", []))
        super().__init__(*args, **kwargs)

    def _create_slice(self, start, stop):
        if start is None:
            start = 0

        if start < BaseUGCList.MAX_PINNED and len(self.iterators) > 1:
            # Use only the pins we need, either all 20 or for Page 2,
            # this slice would usually start at 10 items.
            return self._interpolate_pinned(start, stop)

        return super()._create_slice(start, stop)

    def _interpolate_pinned(self, start=None, stop=None):
        # Preload items from `pinned` iterator.
        pinned = list(self.iterators[0])

        # Preload items from ugc list.
        result = list(self.iterators[1])

        def get_pinned_ugc(id):
            try:
                return [x for x in pinned if x["id"] == id][0]
            except IndexError:
                return None

        # Insert an unpinned ugc where the pinned_ugc_ids
        # array has empty elements.
        for index, ugc_id in enumerate(self._pins):
            if not ugc_id:
                continue
            ugc = get_pinned_ugc(ugc_id)
            if ugc:
                if ugc in result:
                    result.remove(ugc)
                result.insert(index, ugc)

        if start is None:
            return result
        return result[start:stop]

    def __iter__(self):
        """Override __iter__ to ensure pinned stories are correctly
        interpolated and no more than BaseUGCList.MAX_PINNED ugc are returned.
        """
        if len(self.iterators) > 1:
            interpolated = self._interpolate_pinned(
                start=0, stop=BaseUGCList.MAX_PINNED
            )
            # Yield the pinned ugc interpolated with the unpinned.
            for ugc in interpolated:
                yield ugc
        else:
            # Just yield the unpinned.
            for ugc in self.iterators[0][: BaseUGCList.MAX_PINNED]:
                yield ugc


class UGCResourceList(SlumberResourceIterator):
    def __init__(self, *args, **kwargs):
        self._use_cache = kwargs.pop("use_cache", True)
        self._set_cache = kwargs.pop("set_cache", True)
        self._sort_key = kwargs.pop("sort_key", None)
        super().__init__(*args, **kwargs)

    def _fill_cache(self):
        if self._cache_is_full():
            return

        if self._sort_key:
            offset = 0
            limit = None
        else:
            offset = self.offset + len(self._cached_objects)
            limit = BaseUGCList.MAX_PINNED
        from suzuka.conf.sites import current_site_id

        site_id = self.filters.get("site_id") or current_site_id()

        # pinned ugc actually end up with a different limit arg
        # which ensures a unique cache entry, but we shouldn't depend
        # on that, so we have a pinned flag in the cache key too,
        pinned = "id__in" in self.filters
        cache_key = "ugc-list-%s-%s-%s-%s-%s" % (
            site_id,
            self.filters["ugc_list_id"],
            offset,
            limit,
            pinned,
        )
        # extend the key with any additional filters being used which will
        # cause the api result to vary.
        if self.filters:
            # sort filters for better cache hits
            filters_str = json.dumps(
                self.filters, sort_keys=True, separators=(",", ":")
            )
            filters_hash = hashlib.md5(filters_str.encode()).hexdigest()
            cache_key = f"{cache_key}-{filters_hash}"

        data = (
            cache_get(cache_key)
            if self._use_cache and UGC_LIST_CACHE_TIMEOUT > 0
            else None
        )

        if data is None:
            if limit is None and pinned:
                # Sort key breaks without complete result set. Force API
                # API to return all results, otherwise the default limit
                # of 20 results will be returned.
                limit = len(self.filters["id__in"].split(","))
            filters = dict(self.filters)
            del filters["ugc_list_id"]
            data = self.api_resource.get(offset=offset, limit=limit, **filters)
            if self._sort_key:
                data["results"].sort(key=self._sort_key)
            if self._set_cache and UGC_LIST_CACHE_TIMEOUT > 0:
                cache_set(cache_key, data, UGC_LIST_CACHE_TIMEOUT)

        self._total_objects = data["count"] or 0

        for obj in data["results"]:
            obj["_sort_key"] = bool(self._sort_key)
            obj["is_pinned"] = pinned
            self._cached_objects.append(obj)

    def _create_class(self):
        obj = super()._create_class()
        obj._use_cache = self._use_cache
        obj._set_cache = self._set_cache
        obj._sort_key = self._sort_key
        return obj
